<route lang="json5">
{
  style: {
    navigationBarTitleText: '学期异动申请',
  },
}
</route>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import ActionButton from '@/components/common/ActionButton.vue'
import TemplateTip from '@/components/common/TemplateTip.vue'
import CollapsibleSection from '@/components/common/CollapsibleSection.vue'
import FileUploader from '@/components/common/FileUploader.vue'
import FormLabel from '@/components/common/FormLabel.vue'
import TextPlaceholder from '@/components/common/TextPlaceholder.vue'
import OptionSelector from '@/components/common/OptionSelector.vue'
import TextareaInput from '@/components/common/TextareaInput.vue'
import FormContainer from '@/components/common/FormContainer.vue'
import FormItem from '@/components/common/FormItem.vue'
import { getSemesterSelect } from '@/service/system'
import { getSchoolChangeApplyFiles, saveSchoolChangeApply } from '@/service/student'
import { useSchoolChangeStore } from '@/store/schoolChange'
import type { SemesterOption } from '@/types/semester'
import type { SchoolChangeApplyFileItem, SchoolChangeApplySaveParams } from '@/types/student'

/** 路由实例 */
const router = useRouter()

/** 学籍异动store */
const schoolChangeStore = useSchoolChangeStore()

/** 是否为编辑模式 */
const isEditMode = computed(() => schoolChangeStore.getIsEditMode())

/** 是否为查看模式 */
const isViewMode = computed(() => schoolChangeStore.getIsViewMode())

/** 当前编辑的申请ID */
const editingId = ref<number | null>(null)

/** 表单数据 */
const formData = ref({
  /** 异动申请学期 */
  semester: '',
  /** 异动类型 */
  changeType: '',
  /** 异动原因 */
  changeReason: '',
  /** 异动原因补充 */
  changeReasonDetails: '',
  /** 相关附件 */
  attachments: [] as Array<{ url: string; name: string }>,
})

/** 异动类型初始列表 */
const initList = [
  { value: '11', label: '休学' },
  { value: '12', label: '复学' },
  { value: '31', label: '退学' },
  { value: '19', label: '保留学籍' },
  { value: '07', label: '保留入学资格' },
]

/** 异动类型选项 */
const changeTypeOptions = ref(initList.map((item) => item.label))

/** 异动类型选择器索引 */
const changeTypeIndex = ref(-1)

/** 当前选择的异动类型值 */
const selectedChangeTypeValue = ref('')

/** 异动原因选项 */
const changeReasonOptions = ref<string[]>([])

/** 异动原因选择器索引 */
const changeReasonIndex = ref(-1)

/** 当前选择的异动原因值 */
const selectedChangeReasonValue = ref('')

/** 当前异动原因列表（用于获取代码值） */
const currentReasonList = ref<{ value: string; label: string }[]>([])

/** 证明材料模板文件列表 */
const templateFiles = ref<SchoolChangeApplyFileItem[]>([])

/** 加载模板文件列表 */
const loadTemplateFiles = async () => {
  try {
    const files = await getSchoolChangeApplyFiles()
    templateFiles.value = files
  } catch (error) {
    console.error('获取模板文件列表失败:', error)
    // 使用默认的模板文件列表作为降级方案
    templateFiles.value = []
  }
}

/** 根据异动类型获取异动原因列表 */
const getTypeList = (code: string) => {
  let list: { value: string; label: string }[] = []
  switch (code) {
    case '07': // 保留入学资格
      list = [
        { value: '11', label: '精神疾病' },
        { value: '12', label: '传染疾病' },
        { value: '19', label: '其它疾病' },
        { value: '26', label: '家长病重' },
        { value: '96', label: '创新创业' },
        { value: '95', label: '社会实践' },
        { value: '94', label: '工作实践' },
        { value: '88', label: '参军' },
        { value: '99', label: '其它' },
      ]
      break
    case '11': // 休学
      list = [
        { value: '11', label: '精神疾病' },
        { value: '12', label: '传染疾病' },
        { value: '13', label: '心理疾病' },
        { value: '19', label: '其它疾病' },
        { value: '24', label: '休学创业' },
        { value: '26', label: '家长病重' },
        { value: '30', label: '自费留学' },
        { value: '27', label: '贫困' },
        { value: '96', label: '创新创业' },
        { value: '95', label: '社会实践' },
        { value: '94', label: '工作实践' },
        { value: '99', label: '其它原因' },
      ]
      break
    case '12': // 复学
      list = [
        { value: '89', label: '参军复员' },
        { value: '90', label: '休学期满' },
        { value: '99', label: '其它' },
      ]
      break
    case '19': // 保留学籍
      list = [{ value: '34', label: '应征入伍' }]
      break
    case '31': // 退学
      list = [
        { value: '22', label: '成绩低劣' },
        { value: '10', label: '疾病' },
        { value: '13', label: '心理疾病' },
        { value: '23', label: '触犯刑法' },
        { value: '21', label: '自动退学' },
        { value: '30', label: '自费留学' },
        { value: '32', label: '勒令退学' },
        { value: '17', label: '取消学籍' },
        { value: '27', label: '贫困' },
        { value: '24', label: '创业' },
        { value: '99', label: '其它' },
      ]
      break
  }
  return list
}

/** 当前学期的学年和学期值 */
const currentSemesterData = ref({
  studyYear: '',
  studyTerm: '',
})

/** 获取学期数据 */
const fetchSemesterData = async () => {
  try {
    const res = await getSemesterSelect()
    // 设置默认学期
    const defaultSemester = res.data.find((item) => item.value === res.default)
    if (defaultSemester) {
      formData.value.semester = defaultSemester.label
      // 解析学年学期值，格式通常为 "2025-2026|1"
      const [studyYear, studyTerm] = defaultSemester.value.split('|')
      currentSemesterData.value = {
        studyYear: studyYear || '2025-2026',
        studyTerm: studyTerm || '1',
      }
    }
  } catch (error) {
    console.error('获取学期数据失败', error)
    // 设置默认值
    formData.value.semester = '2025-2026学年 第1学期'
    currentSemesterData.value = {
      studyYear: '2025-2026',
      studyTerm: '1',
    }
  }
}

/** 异动类型选择器变化 */
const onChangeTypeChange = (e: any) => {
  const index = e.detail.value
  changeTypeIndex.value = index
  formData.value.changeType = changeTypeOptions.value[index]

  // 获取对应的异动类型值
  const selectedType = initList[index]
  if (selectedType) {
    selectedChangeTypeValue.value = selectedType.value
    // 根据异动类型获取对应的异动原因列表
    const reasonList = getTypeList(selectedType.value)
    currentReasonList.value = reasonList
    changeReasonOptions.value = reasonList.map((item) => item.label)
    // 重置异动原因选择
    changeReasonIndex.value = -1
    formData.value.changeReason = ''
    selectedChangeReasonValue.value = ''
  }
}

/** 异动原因选择器变化 */
const onChangeReasonChange = (e: any) => {
  const index = e.detail.value
  changeReasonIndex.value = index
  formData.value.changeReason = changeReasonOptions.value[index]

  // 获取对应的异动原因代码值
  const selectedReason = currentReasonList.value[index]
  if (selectedReason) {
    selectedChangeReasonValue.value = selectedReason.value
  }
}

/** 提交申请 */
const submitApplication = async () => {
  // 表单验证
  if (!formData.value.changeType) {
    uni.showToast({
      title: '请选择异动类型',
      icon: 'none',
    })
    return
  }

  if (!formData.value.changeReason) {
    uni.showToast({
      title: '请选择异动原因',
      icon: 'none',
    })
    return
  }

  try {
    // 显示加载提示
    uni.showLoading({
      title: isEditMode.value ? '更新中...' : '提交中...',
    })

    // 构建附件列表字符串，格式为"文件路径|文件名称"
    const attachmentList = formData.value.attachments
      .map((attachment) => `${attachment.url}|${attachment.name}`)
      .join(',')

    // 构建提交参数
    const submitParams: SchoolChangeApplySaveParams = {
      studyYear: currentSemesterData.value.studyYear,
      studyTerm: currentSemesterData.value.studyTerm,
      changeType: selectedChangeTypeValue.value,
      changeReasonCode: selectedChangeReasonValue.value,
      changeReasonDetails: formData.value.changeReasonDetails,
      changeTypeName: formData.value.changeType,
      changeReasonName: formData.value.changeReason,
      attachmentList,
    }

    // 如果是编辑模式，添加完整的编辑数据
    if (isEditMode.value && editingId.value) {
      const editData = schoolChangeStore.getCurrentEditData()
      if (editData && editData.originalData) {
        const originalData = editData.originalData
        Object.assign(submitParams, {
          id: originalData.id,
          remark: originalData.remark || '',
          create_time: originalData.create_time,
          update_time: originalData.update_time,
          deltag: originalData.deltag || 0,
          operatorUser: originalData.operatorUser,
          applicationType: originalData.applicationType,
          studentCode: originalData.studentCode,
          studentName: originalData.studentName,
          applicationTime: originalData.applicationTime,
          applicationDescription: originalData.applicationDescription,
          counselorApproval: originalData.counselorApproval,
          deptApproval: originalData.deptApproval,
          deptLeaderApproval: originalData.deptLeaderApproval,
          academicAffairsApproval: originalData.academicAffairsApproval,
          _X_ROW_KEY: `row_${originalData.id}`,
        })
      }
    }

    // 调用提交接口
    const result = await saveSchoolChangeApply(submitParams)

    uni.hideLoading()

    uni.showToast({
      title: isEditMode.value ? '更新成功' : '申请提交成功',
      icon: 'success',
    })

    // 清除编辑数据
    if (isEditMode.value) {
      schoolChangeStore.clearEditData()
    }

    // 返回列表页
    setTimeout(() => {
      router.back()
    }, 1500)
  } catch (error) {
    uni.hideLoading()
    console.error('提交申请失败:', error)
    uni.showToast({
      title: isEditMode.value ? '更新失败，请重试' : '提交失败，请重试',
      icon: 'error',
    })
  }
}

/** 返回 */
const goBack = () => {
  // 清除编辑数据和查看模式
  if (isEditMode.value || isViewMode.value) {
    schoolChangeStore.clearEditData()
  }
  router.back()
}

/** 获取文件图标 */
const getFileIcon = (fileName: string) => {
  const ext = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase()
  switch (ext) {
    case 'doc':
    case 'docx':
      return 'file-word'
    case 'pdf':
      return 'file-pdf'
    case 'xls':
    case 'xlsx':
      return 'file-excel'
    case 'ppt':
    case 'pptx':
      return 'file-ppt'
    default:
      return 'file'
  }
}

/** 预览模板文件 */
const previewTemplate = (template: SchoolChangeApplyFileItem) => {
  // 根据文件类型选择预览方式
  const fileExt = template.name.split('.').pop()?.toLowerCase()

  if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(fileExt || '')) {
    // 图片文件直接预览
    uni.previewImage({
      urls: [template.url],
      current: template.url,
    })
  } else {
    // 非图片文件使用webview预览
    uni.navigateTo({
      url: `/pages/public/webview/webview?pdfreview=true&url=${encodeURIComponent(template.url)}&title=${encodeURIComponent(template.name)}`,
      fail: (err) => {
        console.error('打开文件失败', err)
        uni.showToast({
          title: '打开文件失败',
          icon: 'none',
        })
      },
    })
  }
}

/** 下载模板文件 */
const downloadTemplate = (template: SchoolChangeApplyFileItem) => {
  // 直接使用URL进行下载
  uni.downloadFile({
    url: template.url,
    success: (res) => {
      if (res.statusCode === 200) {
        uni.showToast({
          title: '下载成功',
          icon: 'success',
        })
      } else {
        uni.showToast({
          title: '下载失败',
          icon: 'error',
        })
      }
    },
    fail: () => {
      uni.showToast({
        title: '下载失败',
        icon: 'error',
      })
    },
  })
}

/** 初始化编辑数据 */
const initEditData = () => {
  const editData = schoolChangeStore.getCurrentEditData()
  if (editData) {
    editingId.value = editData.id

    // 回填表单数据
    formData.value = {
      semester: editData.semester,
      changeType: editData.changeType,
      changeReason: editData.changeReason,
      changeReasonDetails: editData.changeReasonDetails,
      attachments: [...editData.attachments],
    }

    // 设置选择器索引
    const typeIndex = initList.findIndex((item) => item.value === editData.changeTypeCode)
    if (typeIndex !== -1) {
      changeTypeIndex.value = typeIndex
      selectedChangeTypeValue.value = editData.changeTypeCode

      // 加载对应的异动原因选项
      const reasonList = getTypeList(editData.changeTypeCode)
      currentReasonList.value = reasonList
      changeReasonOptions.value = reasonList.map((item) => item.label)

      // 设置异动原因选择器索引
      const reasonIndex = currentReasonList.value.findIndex(
        (item) => item.value === editData.changeReasonCode,
      )
      if (reasonIndex !== -1) {
        changeReasonIndex.value = reasonIndex
        selectedChangeReasonValue.value = editData.changeReasonCode
      }
    }
  }
}

onMounted(() => {
  // 获取学期数据
  fetchSemesterData()
  // 加载模板文件列表
  loadTemplateFiles()

  // 如果是编辑模式或查看模式，初始化编辑数据
  if (isEditMode.value || isViewMode.value) {
    initEditData()
  }
})
</script>

<template>
  <view class="apply-container bg-[#f2f2f7] min-h-screen">
    <!-- 表单内容 -->
    <FormContainer>
      <!-- 异动申请学期 -->
      <FormItem>
        <FormLabel text="异动申请学期" />
        <TextPlaceholder
          :value="formData.semester"
          placeholder="加载中..."
          :loading="!formData.semester"
        />
      </FormItem>

      <!-- 异动类型 -->
      <FormItem>
        <FormLabel text="异动类型" :required="true" />
        <OptionSelector
          v-model="changeTypeIndex"
          :options="changeTypeOptions"
          :selected-value="formData.changeType"
          placeholder="请选择异动类型"
          :disabled="isViewMode"
          @change="onChangeTypeChange"
        />
      </FormItem>

      <!-- 异动原因 -->
      <FormItem>
        <FormLabel text="异动原因" :required="true" />
        <OptionSelector
          v-model="changeReasonIndex"
          :options="changeReasonOptions"
          :selected-value="formData.changeReason"
          :disabled="!formData.changeType || isViewMode"
          placeholder="请选择异动原因"
          disabled-placeholder="请先选择异动类型"
          @change="onChangeReasonChange"
        />
      </FormItem>

      <!-- 异动原因补充 -->
      <FormItem>
        <FormLabel text="异动原因补充" />
        <TextareaInput
          v-model="formData.changeReasonDetails"
          placeholder="请详细说明异动原因..."
          :maxlength="500"
          :show-count="true"
          :auto-height="true"
          :disabled="isViewMode"
        />
      </FormItem>

      <!-- 证明材料模板 -->
      <FormItem>
        <CollapsibleSection title="证明材料模板" :default-expanded="true">
          <TemplateTip
            type="primary"
            text="学籍异动申请操作（休学、复学、退学异动类型需打印填写下方表格）"
          />

          <view class="template-list">
            <view class="template-item" v-for="(template, index) in templateFiles" :key="index">
              <view class="template-info">
                <wd-icon :name="getFileIcon(template.name)" size="18px" class="mr-2" />
                <text class="template-name">{{ template.name }}</text>
              </view>
              <view class="template-actions">
                <ActionButton
                  type="primary"
                  size="mini"
                  text="预览"
                  @click="previewTemplate(template)"
                />
                <ActionButton
                  type="success"
                  size="mini"
                  text="下载"
                  @click="downloadTemplate(template)"
                />
              </view>
            </view>
          </view>
        </CollapsibleSection>
      </FormItem>

      <!-- 相关附件 -->
      <FormItem margin-bottom="none">
        <TemplateTip type="danger" text="请查看表格中注明需要的相关材料，拍照后进行上传" />
        <FileUploader
          v-model="formData.attachments"
          title="相关附件"
          upload-type="schoolChange"
          tip-text="支持jpg、png、pdf等常见文件格式，最多上传5个文件"
          :disabled="isViewMode"
        />
      </FormItem>
    </FormContainer>

    <!-- 底部操作按钮 -->
    <view
      v-if="!isViewMode"
      class="bottom-actions fixed bottom-0 left-0 right-0 bg-white p-[20rpx] border-t border-gray-100"
    >
      <view class="flex gap-[16rpx]">
        <ActionButton
          type="secondary"
          text="取消"
          @click="goBack"
          class="flex-1 py-[16rpx] text-center"
        />
        <ActionButton
          type="primary"
          :text="isEditMode ? '更新申请' : '提交申请'"
          @click="submitApplication"
          class="flex-1 py-[16rpx] text-center"
        />
      </view>
    </view>

    <!-- 查看模式底部按钮 -->
    <view
      v-if="isViewMode"
      class="bottom-actions fixed bottom-0 left-0 right-0 bg-white p-[20rpx] border-t border-gray-100"
    >
      <ActionButton type="secondary" text="返回" @click="goBack" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.apply-container {
  padding-bottom: 100rpx; // 为底部按钮留出空间
}

// 这些样式已经移动到对应的通用组件中，不再需要

.attachment-remove {
  opacity: 0.8;
  transition: opacity 0.2s;
}

.attachment-item {
  &:hover .attachment-remove {
    opacity: 1;
  }
}

.add-attachment {
  min-height: 120rpx; // 减小附件上传区域高度
  cursor: pointer;
  transition: border-color 0.2s;

  &:hover {
    border-color: #0083ff;
  }
}

.picker-item {
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f5f5f5;
  }

  &:last-child {
    border-bottom: none;
  }
}

// 模板区域样式

.template-list {
  width: 100%;
}

.template-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx;
  margin-bottom: 12rpx;
  background-color: #f9fafb;
  border-radius: 8rpx;
  transition: all 0.2s;

  &:hover {
    background-color: #e6f7ff;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.template-info {
  display: flex;
  flex: 1;
  align-items: center;
  min-width: 0; // 允许文本截断
}

.template-name {
  overflow: hidden;
  font-size: 26rpx;
  color: #333;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.template-actions {
  display: flex;
  flex-shrink: 0;
  gap: 12rpx;
}
</style>
